import React, { useState, FormEvent, useEffect } from 'react';
import { College } from '../../types';
import Table from '../../components/Table';
import Button from '../../components/Button';
import Modal from '../../components/Modal';
import Input from '../../components/Input';
import Card from '../../components/Card';

const API_BASE_URL = 'http://localhost:3000/api';

const CollegeManagementPage: React.FC = () => {
  const [colleges, setColleges] = useState<College[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingCollege, setEditingCollege] = useState<College | null>(null);
  const [newCollegeData, setNewCollegeData] = useState<Partial<College>>({});

  const columns = [
    { header: 'ID', accessor: 'id' as keyof College },
    { header: '学院名称', accessor: 'name' as keyof College },
    {
      header: '操作',
      accessor: 'id' as keyof College,
      render: (college: College) => (
        <div className="space-x-2">
          <Button size="sm" variant="ghost" onClick={() => handleEdit(college)}><i className="fas fa-edit"></i></Button>
          <Button size="sm" variant="danger" onClick={() => handleDelete(college.id)}><i className="fas fa-trash"></i></Button>
        </div>
      ),
    },
  ];

  // 获取所有学院
  const fetchColleges = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/colleges`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '获取学院列表失败');
      }

      const data = await response.json();
      if (data.success) {
        // 后端直接返回colleges数组，不需要.data.colleges
        setColleges(data.data || []);
      } else {
        throw new Error(data.message || '获取学院列表失败');
      }
    } catch (error) {
      console.error('获取学院列表错误:', error);
      const errorMessage = error instanceof Error ? error.message : '获取学院列表失败，请重试';
      alert(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchColleges();
  }, []);

  const handleOpenModal = (college: College | null = null) => {
    setEditingCollege(college);
    setNewCollegeData(college ? { ...college } : { id: '', name: '' });
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingCollege(null);
    setNewCollegeData({});
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewCollegeData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newCollegeData.name) {
        alert("学院名称不能为空！");
        return;
    }

    try {
      const token = localStorage.getItem('token');
      
      if (editingCollege) {
        // 更新学院
        const response = await fetch(`${API_BASE_URL}/colleges/${editingCollege.id}`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: newCollegeData.name
          }),
        });

        if (!response.ok) {
          throw new Error('更新学院失败');
        }

        const data = await response.json();
        if (data.success) {
          await fetchColleges();
          handleCloseModal();
        }
      } else {
        // 创建新学院
        const response = await fetch(`${API_BASE_URL}/colleges`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: newCollegeData.name
          }),
        });

        if (!response.ok) {
          throw new Error('创建学院失败');
        }

        const data = await response.json();
        if (data.success) {
          await fetchColleges();
          handleCloseModal();
        }
      }
    } catch (error) {
      console.error('保存学院错误:', error);
      alert('保存学院失败，请重试');
    }
  };

  const handleEdit = (college: College) => {
    handleOpenModal(college);
  };

  const handleDelete = async (collegeId: string) => {
    if (window.confirm('您确定要删除此学院吗？这将同时删除其下所有专业。')) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_BASE_URL}/colleges/${collegeId}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        const data = await response.json();

        if (!response.ok) {
          // 显示服务器返回的具体错误信息
          alert(data.message || '删除学院失败');
          return;
        }

        if (data.success) {
          alert('学院删除成功');
          await fetchColleges();
        } else {
          alert(data.message || '删除学院失败');
        }
      } catch (error) {
        console.error('删除学院错误:', error);
        alert('删除学院失败，请重试');
      }
    }
  };

  if (isLoading) {
    return (
      <Card title="学院管理">
        <div className="flex justify-center items-center py-8">
          <div className="text-gray-500">加载中...</div>
        </div>
      </Card>
    );
  }

  return (
    <Card title="学院管理" actions={<Button onClick={() => handleOpenModal()} leftIcon={<i className="fas fa-plus mr-2"></i>}>添加学院</Button>}>
      <Table columns={columns} data={colleges} keyExtractor={college => college.id} />
      <Modal isOpen={isModalOpen} onClose={handleCloseModal} title={editingCollege ? '编辑学院' : '添加新学院'}>
        <form onSubmit={handleSubmit} className="space-y-4">
          <Input name="name" label="学院名称" value={newCollegeData.name || ''} onChange={handleInputChange} required />
          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="ghost" onClick={handleCloseModal}>取消</Button>
            <Button type="submit">{editingCollege ? '保存更改' : '添加学院'}</Button>
          </div>
        </form>
      </Modal>
    </Card>
  );
};

export default CollegeManagementPage;