const XLSX = require("xlsx");
const multer = require("multer");
const path = require("path");
const fs = require("fs");
const express = require('express');
const { v4: uuidv4 } = require('uuid');
const { pool } = require('../config/database');
const { authenticateToken, requireRole } = require('../middleware/auth');
const bcrypt = require('bcrypt');

const router = express.Router();

// 配置multer用于文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = 'uploads/';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + '-' + file.originalname);
  }
});

const upload = multer({
  storage: storage,
  fileFilter: function (req, file, cb) {
    const allowedTypes = ['.xlsx', '.xls'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('只支持Excel文件格式 (.xlsx, .xls)'));
    }
  },
  limits: {
    fileSize: 5 * 1024 * 1024 // 限制5MB
  }
});

// 获取用户列表
router.get('/', authenticateToken, requireRole(['系统管理员']), async (req, res) => {
  try {
    const { page = 1, limit = 10, role, college_id, dorm_building_id, search } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE 1=1';
    const params = [];

    if (role) {
      whereClause += ' AND u.role = ?';
      params.push(role);
    }

    if (college_id) {
      whereClause += ' AND u.college_id = ?';
      params.push(college_id);
    }

    if (dorm_building_id) {
      whereClause += ' AND u.dorm_building_id = ?';
      params.push(dorm_building_id);
    }

    if (search) {
      whereClause += ' AND (u.name LIKE ? OR u.email LIKE ? OR u.phone LIKE ?)';
      const searchTerm = `%${search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    // 查询总数
    const [countResult] = await pool.execute(
      `SELECT COUNT(*) as total FROM users u ${whereClause}`,
      params
    );

    // 查询用户列表
    const [users] = await pool.execute(
      `SELECT u.*, c.name as college_name, m.name as major_name, db.name as dorm_building_name, r.room_number
       FROM users u
       LEFT JOIN colleges c ON u.college_id = c.id
       LEFT JOIN majors m ON u.major_id = m.id
       LEFT JOIN dorm_buildings db ON u.dorm_building_id = db.id
       LEFT JOIN rooms r ON u.room_id = r.id
       ${whereClause}
       ORDER BY u.created_at DESC
       LIMIT ? OFFSET ?`,
      [...params, parseInt(limit), offset]
    );

    // 移除密码字段
    const usersWithoutPassword = users.map(user => {
      const { password, ...userWithoutPassword } = user;
      return userWithoutPassword;
    });

    res.json({
      success: true,
      data: {
        users: usersWithoutPassword,
        pagination: {
          total: countResult[0].total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(countResult[0].total / limit)
        }
      }
    });

  } catch (error) {
    console.error('获取用户列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 获取当前用户的详细信息（包括宿舍信息）
router.get('/me/detailed', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;

    const [users] = await pool.execute(
      `SELECT u.*, 
              c.name as college_name, 
              m.name as major_name, 
              db.name as dorm_building_name, 
              r.room_number,
              r.floor,
              r.type,
              r.capacity,
              r.occupied_beds,
              b.bed_number,
              b.status as bed_status
       FROM users u
       LEFT JOIN colleges c ON u.college_id = c.id
       LEFT JOIN majors m ON u.major_id = m.id
       LEFT JOIN dorm_buildings db ON u.dorm_building_id = db.id
       LEFT JOIN rooms r ON u.room_id = r.id
       LEFT JOIN beds b ON b.student_id = u.id AND b.room_id = u.room_id
       WHERE u.id = ?`,
      [userId]
    );

    if (users.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    const { password, ...userWithoutPassword } = users[0];

    res.json({
      success: true,
      data: userWithoutPassword
    });

  } catch (error) {
    console.error('获取用户详细信息错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 获取所有维修人员列表 (宿舍管理员可用)
router.get('/repair-staff', authenticateToken, requireRole(['宿舍管理员']), async (req, res) => {
  try {
    const [repairStaff] = await pool.execute(
      'SELECT id, name FROM users WHERE role = "维修人员" ORDER BY name ASC'
    );

    res.json({
      success: true,
      data: repairStaff
    });

  } catch (error) {
    console.error('获取维修人员列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 获取单个用户
router.get('/:id', authenticateToken, requireRole(['系统管理员', '宿舍管理员', '学生', '维修人员']), async (req, res) => {
  try {
    const { id } = req.params;
    const userRole = req.user.role;

    let whereClause = 'WHERE u.id = ?';
    const params = [id];

    // 宿舍管理员只能查看自己宿舍楼的学生
    if (userRole === '宿舍管理员') {
      whereClause += ' AND (u.role != \'学生\' OR u.dorm_building_id IN (SELECT id FROM dorm_buildings WHERE assigned_admin_id = ?))';
      params.push(req.user.id);
    }

    // 学生只能查看自己的信息
    if (userRole === '学生') {
      whereClause += ' AND u.id = ?';
      params.push(req.user.id);
    }

    // 维修人员只能查看自己的信息
    if (userRole === '维修人员') {
      whereClause += ' AND u.id = ?';
      params.push(req.user.id);
    }

    const [users] = await pool.execute(
      `SELECT u.*, c.name as college_name, m.name as major_name, db.name as dorm_building_name, r.room_number
       FROM users u
       LEFT JOIN colleges c ON u.college_id = c.id
       LEFT JOIN majors m ON u.major_id = m.id
       LEFT JOIN dorm_buildings db ON u.dorm_building_id = db.id
       LEFT JOIN rooms r ON u.room_id = r.id
       ${whereClause}`,
      params
    );

    if (users.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在或无权访问'
      });
    }

    const { password, ...userWithoutPassword } = users[0];

    res.json({
      success: true,
      data: userWithoutPassword
    });

  } catch (error) {
    console.error('获取用户错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 创建用户
router.post('/', authenticateToken, requireRole(['系统管理员']), async (req, res) => {
  try {
    const {
      name,
      email,
      password,
      phone,
      role,
      college_id,
      major_id,
      dorm_building_id,
      room_id,
      emergency_contact_name,
      emergency_contact_phone
    } = req.body;

    if (!name || !email || !password || !role) {
      return res.status(400).json({
        success: false,
        message: '姓名、邮箱、密码和角色不能为空'
      });
    }

    // 检查邮箱是否已存在
    const [existingUsers] = await pool.execute(
      'SELECT id FROM users WHERE email = ?',
      [email]
    );

    if (existingUsers.length > 0) {
      return res.status(400).json({
        success: false,
        message: '邮箱已存在'
      });
    }

    // 加密密码
    const crypto = require('crypto');
    const hashedPassword = crypto.createHash('md5').update(password).digest('hex');

    const userId = uuidv4();

    await pool.execute(
      `INSERT INTO users (id, name, email, password, phone, role, college_id, major_id, 
                         dorm_building_id, room_id, emergency_contact_name, emergency_contact_phone)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [userId, name, email, hashedPassword, phone, role, college_id, major_id,
       dorm_building_id, room_id, emergency_contact_name, emergency_contact_phone]
    );

    // 如果是宿舍管理员，更新宿舍楼的管理员信息
    if (role === '宿舍管理员' && dorm_building_id) {
      await pool.execute(
        'UPDATE dorm_buildings SET assigned_admin_id = ? WHERE id = ?',
        [userId, dorm_building_id]
      );
    }

    res.status(201).json({
      success: true,
      message: '用户创建成功',
      data: { id: userId }
    });

  } catch (error) {
    console.error('创建用户错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 更新用户
router.put('/:id', authenticateToken, requireRole(['系统管理员']), async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      email,
      phone,
      role,
      college_id,
      major_id,
      dorm_building_id,
      room_id,
      emergency_contact_name,
      emergency_contact_phone
    } = req.body;

    // 检查用户是否存在
    const [existingUsers] = await pool.execute(
      'SELECT id FROM users WHERE id = ?',
      [id]
    );

    if (existingUsers.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 检查邮箱是否被其他用户使用
    if (email) {
      const [emailUsers] = await pool.execute(
        'SELECT id FROM users WHERE email = ? AND id != ?',
        [email, id]
      );

      if (emailUsers.length > 0) {
        return res.status(400).json({
          success: false,
          message: '邮箱已被其他用户使用'
        });
      }
    }

    // 构建更新字段
    const updateFields = [];
    const updateValues = [];

    if (name) {
      updateFields.push('name = ?');
      updateValues.push(name);
    }
    if (email) {
      updateFields.push('email = ?');
      updateValues.push(email);
    }
    if (phone !== undefined) {
      updateFields.push('phone = ?');
      updateValues.push(phone);
    }
    if (role) {
      updateFields.push('role = ?');
      updateValues.push(role);
    }
    if (college_id !== undefined) {
      updateFields.push('college_id = ?');
      updateValues.push(college_id);
    }
    if (major_id !== undefined) {
      updateFields.push('major_id = ?');
      updateValues.push(major_id);
    }
    if (dorm_building_id !== undefined) {
      updateFields.push('dorm_building_id = ?');
      updateValues.push(dorm_building_id);
    }
    if (room_id !== undefined) {
      updateFields.push('room_id = ?');
      updateValues.push(room_id);
    }
    if (emergency_contact_name !== undefined) {
      updateFields.push('emergency_contact_name = ?');
      updateValues.push(emergency_contact_name);
    }
    if (emergency_contact_phone !== undefined) {
      updateFields.push('emergency_contact_phone = ?');
      updateValues.push(emergency_contact_phone);
    }

    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        message: '没有要更新的字段'
      });
    }

    updateValues.push(id);

    await pool.execute(
      `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );

    // 如果是宿舍管理员，更新宿舍楼的管理员信息
    if (role === '宿舍管理员' && dorm_building_id) {
      await pool.execute(
        'UPDATE dorm_buildings SET assigned_admin_id = ? WHERE id = ?',
        [id, dorm_building_id]
      );
    }

    res.json({
      success: true,
      message: '用户更新成功'
    });

  } catch (error) {
    console.error('更新用户错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 删除用户
router.delete('/:id', authenticateToken, requireRole(['系统管理员']), async (req, res) => {
  try {
    const { id } = req.params;

    // 检查用户是否存在
    const [existingUsers] = await pool.execute(
      'SELECT id FROM users WHERE id = ?',
      [id]
    );

    if (existingUsers.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    await pool.execute('DELETE FROM users WHERE id = ?', [id]);

    res.json({
      success: true,
      message: '用户删除成功'
    });

  } catch (error) {
    console.error('删除用户错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 获取宿管楼栋的学生列表
router.get('/dorm-building/students', authenticateToken, requireRole(['宿舍管理员']), async (req, res) => {
  try {
    const dorm_building_id = req.user.dorm_building_id; // 从req.user中获取宿管的楼栋ID

    if (!dorm_building_id) {
      return res.status(400).json({
        success: false,
        message: '宿管未分配楼栋'
      });
    }

    // 查询该楼栋的所有学生
    const [students] = await pool.execute(
      `SELECT u.*, c.name as college_name, m.name as major_name, db.name as dorm_building_name, r.room_number
       FROM users u
       LEFT JOIN colleges c ON u.college_id = c.id
       LEFT JOIN majors m ON u.major_id = m.id
       LEFT JOIN dorm_buildings db ON u.dorm_building_id = db.id
       LEFT JOIN rooms r ON u.room_id = r.id
       WHERE u.role = '学生' AND u.dorm_building_id = ?
       ORDER BY u.name`,
      [dorm_building_id]
    );

    // 移除密码字段
    const studentsWithoutPassword = students.map(student => {
      const { password, ...studentWithoutPassword } = student;
      return studentWithoutPassword;
    });

    res.json({
      success: true,
      data: studentsWithoutPassword
    });

  } catch (error) {
    console.error('获取楼栋学生列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 批量导入用户
router.post('/batch-import', authenticateToken, requireRole(['系统管理员']), async (req, res) => {
  try {
    const { type, data } = req.body;

    if (!type || !data) {
      return res.status(400).json({
        success: false,
        message: '导入类型和数据不能为空'
      });
    }

    // 解析CSV数据
    const lines = data.trim().split('\n');
    const headers = lines[0].split(',');
    const records = lines.slice(1).map(line => {
      const values = line.split(',');
      const record = {};
      headers.forEach((header, index) => {
        record[header.trim()] = values[index] ? values[index].trim() : '';
      });
      return record;
    });

    let importedCount = 0;
    const errors = [];

    for (let i = 0; i < records.length; i++) {
      const record = records[i];
      try {
        let userId, role, collegeId = null, majorId = null, dormBuildingId = null;

        // 根据类型处理不同的字段
        switch (type) {
          case 'students':
            role = '学生';
            
            // 查找学院
            if (record['学院']) {
              const [colleges] = await pool.execute(
                'SELECT id FROM colleges WHERE name = ?',
                [record['学院']]
              );
              if (colleges.length > 0) {
                collegeId = colleges[0].id;
              }
            }

            // 查找专业
            if (record['专业'] && collegeId) {
              const [majors] = await pool.execute(
                'SELECT id FROM majors WHERE name = ? AND college_id = ?',
                [record['专业'], collegeId]
              );
              if (majors.length > 0) {
                majorId = majors[0].id;
              }
            }

            // 查找宿舍楼
            if (record['宿舍楼']) {
              const [buildings] = await pool.execute(
                'SELECT id FROM dorm_buildings WHERE name = ?',
                [record['宿舍楼']]
              );
              if (buildings.length > 0) {
                dormBuildingId = buildings[0].id;
              } else {
                throw new Error(`第${i + 2}行: 未找到宿舍楼"${record['宿舍楼']}"，请检查名称是否与数据库一致`);
              }
            }

            userId = uuidv4();
            await pool.execute(
              `INSERT INTO users (id, name, email, password, role, phone, college_id, major_id, dorm_building_id, emergency_contact_name, emergency_contact_phone) 
               VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
              [
                userId,
                record['姓名'],
                record['邮箱'],
                require('crypto').createHash('md5').update('password123').digest('hex'), // 默认密码
                role,
                record['电话'] || null,
                collegeId,
                majorId,
                dormBuildingId,
                record['紧急联系人姓名'] || null,
                record['紧急联系人电话'] || null
              ]
            );
            break;

          case 'dorm_admins':
            role = '宿舍管理员';

            // 查找宿舍楼
            if (record['宿舍楼']) {
              const [buildings] = await pool.execute(
                'SELECT id FROM dorm_buildings WHERE name = ?',
                [record['宿舍楼']]
              );
              if (buildings.length > 0) {
                dormBuildingId = buildings[0].id;
              } else {
                throw new Error(`第${i + 2}行: 未找到宿舍楼"${record['宿舍楼']}"，请检查名称是否与数据库一致`);
              }
            }

            userId = uuidv4();
            await pool.execute(
              `INSERT INTO users (id, name, email, password, role, phone, dorm_building_id) 
               VALUES (?, ?, ?, ?, ?, ?, ?)`,
              [
                userId,
                record['姓名'],
                record['邮箱'],
                require('crypto').createHash('md5').update('password123').digest('hex'), // 默认密码
                role,
                record['电话'] || null,
                dormBuildingId
              ]
            );

            // 更新宿舍楼的管理员信息
            if (dormBuildingId) {
              await pool.execute(
                'UPDATE dorm_buildings SET assigned_admin_id = ? WHERE id = ?',
                [userId, dormBuildingId]
              );
            }
            break;

          case 'repair_staff':
            role = '维修人员';
            userId = uuidv4();
            await pool.execute(
              `INSERT INTO users (id, name, email, password, role, phone) 
               VALUES (?, ?, ?, ?, ?, ?)`,
              [
                userId,
                record['姓名'],
                record['邮箱'],
                require('crypto').createHash('md5').update('password123').digest('hex'), // 默认密码
                role,
                record['电话'] || null
              ]
            );
            break;

          default:
            throw new Error('不支持的导入类型');
        }

        importedCount++;
      } catch (error) {
        errors.push(`第${i + 2}行: ${error.message}`);
      }
    }

    res.json({
      success: true,
      message: `成功导入 ${importedCount} 个用户${errors.length > 0 ? `，${errors.length} 个失败` : ''}`,
      data: {
        importedCount,
        errors: errors.length > 0 ? errors : undefined
      }
    });

  } catch (error) {
    console.error('批量导入用户错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 批量分配宿舍
router.post('/batch-assign', authenticateToken, requireRole(['宿舍管理员']), async (req, res) => {
  try {
    const { major_id, class_name, dorm_building_id } = req.body;

    if (!major_id || !class_name || !dorm_building_id) {
      return res.status(400).json({
        success: false,
        message: '专业、班级和宿舍楼ID不能为空'
      });
    }

    // 查找该专业和班级下未分配宿舍的学生
    const [students] = await pool.execute(
      `SELECT u.id, u.name, u.email 
       FROM users u
       WHERE u.role = '学生' 
       AND u.major_id = ? 
       AND u.class_name = ?
       AND (u.dorm_building_id IS NULL OR u.dorm_building_id != ?)`,
      [major_id, class_name, dorm_building_id]
    );

    if (students.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到该专业和班级下需要分配宿舍的学生'
      });
    }

    // 查找该宿舍楼下的空闲床位
    const [vacantBeds] = await pool.execute(
      `SELECT b.id, b.room_id, b.bed_number, r.room_number
       FROM beds b
       JOIN rooms r ON b.room_id = r.id
       WHERE r.dorm_building_id = ? AND b.status = '空闲'
       ORDER BY r.room_number, b.bed_number`,
      [dorm_building_id]
    );

    if (vacantBeds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '该宿舍楼下没有空闲床位'
      });
    }

    let assignedCount = 0;
    const errors = [];

    // 为每个学生分配床位
    for (let i = 0; i < Math.min(students.length, vacantBeds.length); i++) {
      const student = students[i];
      const bed = vacantBeds[i];

      try {
        // 更新床位状态
        await pool.execute(
          'UPDATE beds SET status = ?, student_id = ? WHERE id = ?',
          ['已入住', student.id, bed.id]
        );

        // 更新学生信息
        await pool.execute(
          'UPDATE users SET dorm_building_id = ?, room_id = ? WHERE id = ?',
          [dorm_building_id, bed.room_id, student.id]
        );

        assignedCount++;
      } catch (error) {
        errors.push(`学生 ${student.name}: ${error.message}`);
      }
    }

    res.json({
      success: true,
      message: `成功为 ${assignedCount} 名学生分配宿舍${errors.length > 0 ? `，${errors.length} 个失败` : ''}`,
      data: {
        assignedCount,
        errors: errors.length > 0 ? errors : undefined
      }
    });

  } catch (error) {
    console.error('批量分配宿舍错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// Excel文件预览
router.post('/preview-excel', authenticateToken, requireRole(['系统管理员']), upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请上传Excel文件'
      });
    }

    const { type } = req.body;
    if (!type) {
      return res.status(400).json({
        success: false,
        message: '导入类型不能为空'
      });
    }

    // 读取Excel文件
    const workbook = XLSX.readFile(req.file.path, { 
      type: 'file',
      cellDates: true,
      cellNF: false,
      cellText: false
    });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet, { 
      header: 1,
      defval: '',
      blankrows: false
    });

    if (data.length < 2) {
      return res.status(400).json({
        success: false,
        message: 'Excel文件至少需要包含标题行和一行数据'
      });
    }

    // 获取标题行并清理BOM
    const headers = data[0].map(header => {
      if (typeof header === 'string') {
        // 移除可能的BOM字符
        return header.replace(/^\uFEFF/, '').trim();
      }
      return header;
    });
    
    const preview = data.slice(1, 6).map(row => {
      const obj = {};
      headers.forEach((header, index) => {
        obj[header] = row[index] || '';
      });
      return obj;
    });

    // 删除临时文件
    fs.unlinkSync(req.file.path);

    res.json({
      success: true,
      data: {
        preview,
        totalRows: data.length - 1
      }
    });

  } catch (error) {
    console.error('Excel预览错误:', error);
    // 删除临时文件
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    res.status(500).json({
      success: false,
      message: '预览文件失败'
    });
  }
});

// 下载导入模板
router.get('/download-template/:type', authenticateToken, requireRole(['系统管理员']), async (req, res) => {
  try {
    const { type } = req.params;
    
    let templateData = '';
    let filename = '';
    
    switch (type) {
      case 'students':
        templateData = '姓名,邮箱,电话,学院,专业,班级,紧急联系人姓名,紧急联系人电话\n张三,<EMAIL>,13800138001,计算机科学与技术学院,软件工程,软件2021-1班,张父,13900139001\n李四,<EMAIL>,13800138002,机械工程学院,机械设计制造及其自动化,机械2021-2班,李父,13900139002';
        filename = '学生导入模板.csv';
        break;
      case 'dorm_admins':
        templateData = '姓名,邮箱,电话,宿舍楼\n王宿管,<EMAIL>,13800138003,1号楼\n赵宿管,<EMAIL>,13800138004,2号楼';
        filename = '宿舍管理员导入模板.csv';
        break;
      case 'repair_staff':
        templateData = '姓名,邮箱,电话,专业领域\n陈师傅,<EMAIL>,13800138005,水电维修\n刘师傅,<EMAIL>,13800138006,家具维修';
        filename = '维修人员导入模板.csv';
        break;
      default:
        return res.status(400).json({
          success: false,
          message: '不支持的模板类型'
        });
    }

    // 添加UTF-8 BOM头
    const BOM = Buffer.from([0xEF, 0xBB, 0xBF]);
    const content = Buffer.concat([BOM, Buffer.from(templateData, 'utf8')]);

    res.setHeader('Content-Type', 'text/csv; charset=utf-8');
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`);
    res.setHeader('Content-Length', content.length);
    
    res.send(content);

  } catch (error) {
    console.error('下载模板错误:', error);
    res.status(500).json({
      success: false,
      message: '下载模板失败'
    });
  }
});

// Excel批量导入用户
router.post('/batch-import-excel', authenticateToken, requireRole(['系统管理员']), upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请上传Excel文件'
      });
    }

    const { type } = req.body;
    if (!type) {
      return res.status(400).json({
        success: false,
        message: '导入类型不能为空'
      });
    }

    // 读取Excel文件
    const workbook = XLSX.readFile(req.file.path, { 
      type: 'file',
      cellDates: true,
      cellNF: false,
      cellText: false
    });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet, { 
      header: 1,
      defval: '',
      blankrows: false
    });

    if (data.length < 2) {
      return res.status(400).json({
        success: false,
        message: 'Excel文件至少需要包含标题行和一行数据'
      });
    }

    // 获取标题行并清理BOM
    const headers = data[0].map(header => {
      if (typeof header === 'string') {
        // 移除可能的BOM字符
        return header.replace(/^\uFEFF/, '').trim();
      }
      return header;
    });
    
    const records = data.slice(1).map(row => {
      const obj = {};
      headers.forEach((header, index) => {
        obj[header] = row[index] || '';
      });
      return obj;
    });

    let importedCount = 0;
    const errors = [];

    for (let i = 0; i < records.length; i++) {
      const record = records[i];
      try {
        let userId, role, collegeId = null, majorId = null, dormBuildingId = null;

        // 根据类型处理不同的字段
        switch (type) {
          case 'students':
            role = '学生';
            
            // 查找学院
            if (record['学院']) {
              const [colleges] = await pool.execute(
                'SELECT id FROM colleges WHERE name = ?',
                [record['学院']]
              );
              if (colleges.length > 0) {
                collegeId = colleges[0].id;
              }
            }

            // 查找专业
            if (record['专业'] && collegeId) {
              const [majors] = await pool.execute(
                'SELECT id FROM majors WHERE name = ? AND college_id = ?',
                [record['专业'], collegeId]
              );
              if (majors.length > 0) {
                majorId = majors[0].id;
              }
            }

            // 查找宿舍楼
            if (record['宿舍楼']) {
              const [buildings] = await pool.execute(
                'SELECT id FROM dorm_buildings WHERE name = ?',
                [record['宿舍楼']]
              );
              if (buildings.length > 0) {
                dormBuildingId = buildings[0].id;
              } else {
                throw new Error(`第${i + 2}行: 未找到宿舍楼"${record['宿舍楼']}"，请检查名称是否与数据库一致`);
              }
            }

            userId = uuidv4();
            await pool.execute(
              `INSERT INTO users (id, name, email, password, role, phone, college_id, major_id, dorm_building_id, emergency_contact_name, emergency_contact_phone) 
               VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
              [
                userId,
                record['姓名'],
                record['邮箱'],
                require('crypto').createHash('md5').update('password123').digest('hex'), // 默认密码
                role,
                record['电话'] || null,
                collegeId,
                majorId,
                dormBuildingId,
                record['紧急联系人姓名'] || null,
                record['紧急联系人电话'] || null
              ]
            );
            break;

          case 'dorm_admins':
            role = '宿舍管理员';

            // 查找宿舍楼
            if (record['宿舍楼']) {
              const [buildings] = await pool.execute(
                'SELECT id FROM dorm_buildings WHERE name = ?',
                [record['宿舍楼']]
              );
              if (buildings.length > 0) {
                dormBuildingId = buildings[0].id;
              } else {
                throw new Error(`第${i + 2}行: 未找到宿舍楼"${record['宿舍楼']}"，请检查名称是否与数据库一致`);
              }
            }

            userId = uuidv4();
            await pool.execute(
              `INSERT INTO users (id, name, email, password, role, phone, dorm_building_id) 
               VALUES (?, ?, ?, ?, ?, ?, ?)`,
              [
                userId,
                record['姓名'],
                record['邮箱'],
                require('crypto').createHash('md5').update('password123').digest('hex'), // 默认密码
                role,
                record['电话'] || null,
                dormBuildingId
              ]
            );

            // 更新宿舍楼的管理员信息
            if (dormBuildingId) {
              await pool.execute(
                'UPDATE dorm_buildings SET assigned_admin_id = ? WHERE id = ?',
                [userId, dormBuildingId]
              );
            }
            break;

          case 'repair_staff':
            role = '维修人员';
            userId = uuidv4();
            await pool.execute(
              `INSERT INTO users (id, name, email, password, role, phone) 
               VALUES (?, ?, ?, ?, ?, ?)`,
              [
                userId,
                record['姓名'],
                record['邮箱'],
                require('crypto').createHash('md5').update('password123').digest('hex'), // 默认密码
                role,
                record['电话'] || null
              ]
            );
            break;

          default:
            throw new Error('不支持的导入类型');
        }

        importedCount++;
      } catch (error) {
        errors.push(`第${i + 2}行: ${error.message}`);
      }
    }

    // 删除临时文件
    fs.unlinkSync(req.file.path);

    res.json({
      success: true,
      message: `成功导入 ${importedCount} 个用户${errors.length > 0 ? `，${errors.length} 个失败` : ''}`,
      data: {
        importedCount,
        errors: errors.length > 0 ? errors : undefined
      }
    });

  } catch (error) {
    console.error('Excel批量导入用户错误:', error);
    // 删除临时文件
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

module.exports = router; 


