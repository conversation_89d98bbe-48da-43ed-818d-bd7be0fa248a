const jwt = require('jsonwebtoken');
const { pool } = require('../config/database');

const JWT_SECRET = 'jia-dormitory-secret-key-2024';

// 验证JWT token
const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  // 新增日志
  console.log('收到的Authorization:', authHeader);
  console.log('解析出来的token:', token);

  if (!token) {
    return res.status(401).json({ 
      success: false, 
      message: '访问令牌缺失' 
    });
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    
    // 验证用户是否仍然存在
    const [users] = await pool.execute(
      `SELECT u.id, u.name, u.email, u.role, u.dorm_building_id, db.name as dorm_building_name
       FROM users u
       LEFT JOIN dorm_buildings db ON u.dorm_building_id = db.id
       WHERE u.id = ?`,
      [decoded.userId]
    );

    if (users.length === 0) {
      return res.status(401).json({ 
        success: false, 
        message: '用户不存在' 
      });
    }

    req.user = users[0];
    next();
  } catch (error) {
    return res.status(403).json({ 
      success: false, 
      message: '无效的访问令牌' 
    });
  }
};

// 检查用户角色权限
const requireRole = (allowedRoles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ 
        success: false, 
        message: '用户未认证' 
      });
    }

    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({ 
        success: false, 
        message: '权限不足' 
      });
    }

    next();
  };
};

// 生成JWT token
const generateToken = (userId) => {
  return jwt.sign({ userId }, JWT_SECRET, { expiresIn: '24h' });
};

module.exports = {
  authenticateToken,
  requireRole,
  generateToken,
  JWT_SECRET
}; 